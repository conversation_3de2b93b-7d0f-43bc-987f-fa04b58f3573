import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react';
import { useHistory } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  message,
  Popconfirm,
  Tag,
  Tooltip,
  Row,
  Col,
  Statistic,
  Typography,
  Upload
} from 'antd';
import {
  UploadOutlined,
  EditOutlined,
  DeleteOutlined,
  FileWordOutlined,
  EyeOutlined,
  CopyOutlined,
  SettingOutlined
} from '@ant-design/icons';
import http from '../../libs/http';
import VariableConfigModal from './components/VariableConfigModal';
import styles from './WordTemplateManager.module.less';

const { Title, Text } = Typography;

export default observer(function WordTemplateManager() {
  const history = useHistory();
  const [loading, setLoading] = useState(false);
  const [importing, setImporting] = useState(false);
  const [templates, setTemplates] = useState([]);
  const [variableModalVisible, setVariableModalVisible] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState(null);
  const [statistics, setStatistics] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    draft: 0
  });

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    setLoading(true);
    try {
      const res = await http.get('/api/model-storage/word-templates/');

      // 适配http拦截器的响应格式
      let templates_data;
      if (Array.isArray(res)) {
        // 直接返回数组的情况
        templates_data = res;
      } else if (res.data) {
        // 标准格式 {data: [...], error: ''}
        templates_data = res.data;
      } else {
        // 其他情况
        templates_data = [];
      }

      setTemplates(templates_data);
      calculateStatistics(templates_data);
    } catch (error) {
      message.error('获取模板列表失败：' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const calculateStatistics = (data) => {
    const stats = {
      total: data.length,
      active: data.filter(t => t.status === 'active').length,
      inactive: data.filter(t => t.status === 'inactive').length,
      draft: data.filter(t => t.status === 'draft').length
    };
    setStatistics(stats);
  };

  const handleImport = async (file) => {
    setImporting(true);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('name', file.name.replace(/\.(docx?|doc)$/i, ''));
      formData.append('description', `从文件 ${file.name} 导入的Word模板`);
      formData.append('template_type', 'imported');
      formData.append('status', 'active');

      const res = await http.post('/api/model-storage/word-templates/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (res.error) {
        message.error('导入失败：' + res.error);
      } else {
        message.success('Word模板导入成功');
        await fetchTemplates(); // 刷新列表
      }
    } catch (error) {
      message.error('导入失败：' + error.message);
    } finally {
      setImporting(false);
    }

    return false; // 阻止默认上传行为
  };



  const handleEdit = (record) => {
    // 跳转到编辑页面
    history.push(`/model-storage/word-templates/edit/${record.id}`);
  };



  const handleDelete = async (id) => {
    try {
      const res = await http.delete('/api/model-storage/word-templates/', {
        data: { id }
      });

      if (res.error) {
        message.error(res.error);
      } else {
        message.success('模板删除成功');
        fetchTemplates();
      }
    } catch (error) {
      message.error('删除模板失败：' + error.message);
    }
  };



  const handleCopy = async (record) => {
    try {
      const data = {
        ...record,
        name: `${record.name} - 副本`,
        status: 'draft',
        created_by: 'current_user'
      };
      delete data.id;
      delete data.created_at;
      delete data.updated_at;

      const res = await http.post('/api/model-storage/word-templates/', data);
      if (res.error) {
        message.error(res.error);
      } else {
        message.success('模板复制成功');
        fetchTemplates();
      }
    } catch (error) {
      message.error('复制模板失败：' + error.message);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      active: 'green',
      inactive: 'red',
      draft: 'orange'
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      active: '启用',
      inactive: '禁用',
      draft: '草稿'
    };
    return texts[status] || status;
  };

  const getTypeText = (type) => {
    const types = {
      test_guide: '测试指导',
      report: '测试报告',
      manual: '使用手册',
      specification: '技术规范'
    };
    return types[type] || type;
  };

  const columns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text, record) => (
        <Space>
          <FileWordOutlined style={{ color: '#1890ff' }} />
          <Text strong>{text}</Text>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text || '-'}
        </Tooltip>
      ),
    },
    {
      title: '类型',
      dataIndex: 'template_type',
      key: 'template_type',
      width: 100,
      render: (type) => (
        <Tag color="blue">{getTypeText(type)}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '使用次数',
      dataIndex: 'usage_count',
      key: 'usage_count',
      width: 100,
      render: (count) => (
        <Text type="secondary">{count || 0}</Text>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time) => (
        <Text type="secondary">{time}</Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="复制">
            <Button
              type="text"
              icon={<CopyOutlined />}
              size="small"
              onClick={() => handleCopy(record)}
            />
          </Tooltip>
          <Tooltip title="配置变量">
            <Button
              type="text"
              icon={<SettingOutlined />}
              size="small"
              onClick={() => handleConfigVariables(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个模板吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleViewDetails = (record) => {
    // TODO: 实现查看详情功能
    message.info('查看详情功能开发中...');
  };

  const handleConfigVariables = async (record) => {
    try {
      // 获取模板详情和变量
      const res = await http.get(`/api/model-storage/word-templates/?id=${record.id}`);
      if (res.error) {
        message.error(res.error);
      } else {
        setCurrentTemplate(res.data);
        setVariableModalVisible(true);
      }
    } catch (error) {
      message.error('获取模板详情失败：' + error.message);
    }
  };

  const handleVariableConfigSave = async (variables) => {
    try {
      const data = {
        id: currentTemplate.id,
        variables: variables.map(v => ({
          variable_name: v.variable_name,
          display_name: v.display_name,
          variable_type: v.variable_type,
          default_value: v.default_value,
          options: v.options || [],
          is_required: v.is_required,
          validation_rule: v.validation_rule,
          sort_order: v.sort_order,
          group_name: v.group_name,
          help_text: v.help_text,
          placeholder: v.placeholder
        }))
      };

      const res = await http.put('/api/model-storage/word-templates/', data);
      if (res.error) {
        message.error(res.error);
      } else {
        message.success('变量配置保存成功');
        setVariableModalVisible(false);
        fetchTemplates(); // 刷新列表
      }
    } catch (error) {
      message.error('保存变量配置失败：' + error.message);
    }
  };



  return (
    <div className={styles.container}>
      <Card className={styles.headerCard}>
        <Row gutter={24}>
          <Col span={6}>
            <Statistic
              title="总模板数"
              value={statistics.total}
              prefix={<FileWordOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="启用中"
              value={statistics.active}
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="已禁用"
              value={statistics.inactive}
              valueStyle={{ color: '#cf1322' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="草稿"
              value={statistics.draft}
              valueStyle={{ color: '#d48806' }}
            />
          </Col>
        </Row>
      </Card>

      <Card
        title={
          <Space>
            <FileWordOutlined />
            <Title level={4} style={{ margin: 0 }}>Word模板管理</Title>
          </Space>
        }
        extra={
          <Upload
            name="file"
            showUploadList={false}
            customRequest={({ file }) => handleImport(file)}
            beforeUpload={file => {
              const isWordFile = file.name.toLowerCase().endsWith('.docx') ||
                                file.name.toLowerCase().endsWith('.doc');
              if (!isWordFile) {
                message.error('只能上传 .docx 或 .doc 格式的Word文档');
                return false;
              }
              return true;
            }}
            disabled={importing}
          >
            <Button
              type="primary"
              icon={<UploadOutlined />}
              loading={importing}
            >
              导入模板
            </Button>
          </Upload>
        }
      >
        <Table
          columns={columns}
          dataSource={templates}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <VariableConfigModal
        visible={variableModalVisible}
        onCancel={() => setVariableModalVisible(false)}
        onOk={handleVariableConfigSave}
        template={currentTemplate}
        variables={currentTemplate?.variables || []}
      />
    </div>
  );
});
