import React, { useState } from 'react';
import { Card, Button, Space, Input, message, Divider, Typography, Alert } from 'antd';
import { 
  FormatPainterOutlined, 
  ClearOutlined, 
  CheckOutlined,
  FileTextOutlined,
  WarningOutlined
} from '@ant-design/icons';

const { TextArea } = Input;
const { Text, Title } = Typography;

/**
 * 文本格式规范工具
 * 用于清理和规范化富文本内容，确保Word生成时格式正确
 */
export default function TextFormatter({ value, onChange, placeholder }) {
  const [formattedText, setFormattedText] = useState(value || '');
  const [isFormatting, setIsFormatting] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(true); // 默认收起状态

  // 格式化规则
  const formatRules = [
    { name: '清理HTML标签', enabled: true },
    { name: '规范化空白字符', enabled: true },
    { name: '统一换行符', enabled: true },
    { name: '移除多余空行', enabled: true },
    { name: '规范化标点符号', enabled: true },
    { name: '保留基本格式标记', enabled: true }
  ];

  // 文本清理和格式化函数 - 针对Word文档优化
  const cleanAndFormatText = (text) => {
    if (!text) return '';

    let cleaned = text;

    // 1. 保留重要的格式标记，转换为Word友好的标记
    const formatMap = {
      '<strong>': '**',
      '</strong>': '**',
      '<b>': '**',
      '</b>': '**',
      '<em>': '*',
      '</em>': '*',
      '<i>': '*',
      '</i>': '*',
      '<u>': '_',
      '</u>': '_',
      '<h1>': '\n# ',
      '</h1>': '\n',
      '<h2>': '\n## ',
      '</h2>': '\n',
      '<h3>': '\n### ',
      '</h3>': '\n',
      '<h4>': '\n#### ',
      '</h4>': '\n',
      '<p>': '\n',
      '</p>': '\n',
      '<div>': '\n',
      '</div>': '\n',
      '<br>': '\n',
      '<br/>': '\n',
      '<br />': '\n',
      '<li>': '\n• ',
      '</li>': '',
      '<ul>': '\n',
      '</ul>': '\n',
      '<ol>': '\n',
      '</ol>': '\n',
      '<code>': '`',
      '</code>': '`',
      '<pre>': '\n```\n',
      '</pre>': '\n```\n'
    };

    // 应用格式映射
    Object.entries(formatMap).forEach(([html, replacement]) => {
      const regex = new RegExp(html.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
      cleaned = cleaned.replace(regex, replacement);
    });

    // 2. 移除所有剩余的HTML标签
    cleaned = cleaned.replace(/<[^>]*>/g, '');

    // 3. 解码HTML实体
    const entityMap = {
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&#39;': "'",
      '&nbsp;': ' ',
      '&copy;': '©',
      '&reg;': '®',
      '&trade;': '™'
    };

    Object.entries(entityMap).forEach(([entity, char]) => {
      cleaned = cleaned.replace(new RegExp(entity, 'g'), char);
    });

    // 4. 规范化空白字符
    cleaned = cleaned.replace(/\t/g, '    '); // 制表符转为4个空格
    cleaned = cleaned.replace(/\r\n/g, '\n'); // 统一换行符
    cleaned = cleaned.replace(/\r/g, '\n');

    // 5. 清理多余的空白
    cleaned = cleaned.replace(/[ ]+/g, ' '); // 多个空格合并为一个
    cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n'); // 多个空行合并为两个

    // 6. 规范化标点符号（中文环境）
    cleaned = cleaned.replace(/，\s+/g, '，'); // 逗号后多余空格
    cleaned = cleaned.replace(/。\s+/g, '。'); // 句号后多余空格
    cleaned = cleaned.replace(/；\s+/g, '；'); // 分号后多余空格
    cleaned = cleaned.replace(/：\s+/g, '：'); // 冒号后多余空格

    // 7. 清理首尾空白
    cleaned = cleaned.trim();

    return cleaned;
  };

  // 执行格式化
  const handleFormat = () => {
    setIsFormatting(true);
    
    try {
      const formatted = cleanAndFormatText(formattedText);
      setFormattedText(formatted);
      
      if (onChange) {
        onChange(formatted);
      }
      
      message.success('文本格式化完成！');
    } catch (error) {
      message.error('格式化失败：' + error.message);
    } finally {
      setIsFormatting(false);
    }
  };

  // 清空文本
  const handleClear = () => {
    setFormattedText('');
    if (onChange) {
      onChange('');
    }
  };

  // 应用格式化结果
  const handleApply = () => {
    if (onChange) {
      onChange(formattedText);
    }
    message.success('已应用格式化文本到下方编辑器');
  };

  // 检测文本问题
  const detectIssues = (text) => {
    const issues = [];
    
    if (!text) return issues;

    // 检测HTML标签
    if (/<[^>]+>/.test(text)) {
      issues.push('包含HTML标签，将在Word中显示异常');
    }

    // 检测多余空白
    if (/\s{3,}/.test(text)) {
      issues.push('包含多余的空白字符，影响Word排版');
    }

    // 检测多余换行
    if (/\n\s*\n\s*\n/.test(text)) {
      issues.push('包含多余的空行，影响Word段落格式');
    }

    // 检测特殊字符
    if (/[\u0000-\u001F\u007F-\u009F]/.test(text)) {
      issues.push('包含控制字符，可能导致Word显示问题');
    }

    // 检测复杂HTML结构
    if (/<table|<form|<script|<style/.test(text)) {
      issues.push('包含复杂HTML结构，Word无法正确处理');
    }

    // 检测内联样式
    if (/style\s*=/.test(text)) {
      issues.push('包含内联样式，Word中将丢失格式');
    }

    return issues;
  };

  const issues = detectIssues(formattedText);

  return (
    <Card
      title={
        <Space>
          <FormatPainterOutlined />
          <span>文本格式规范工具</span>
          <Button
            type="text"
            size="small"
            onClick={() => setIsCollapsed(!isCollapsed)}
            icon={isCollapsed ? <CheckOutlined /> : <ClearOutlined />}
          >
            {isCollapsed ? '展开' : '收起'}
          </Button>
        </Space>
      }
      size="small"
      style={{ marginBottom: 16 }}
    >
      {/* 问题检测 - 始终显示 */}
      {issues.length > 0 ? (
        <Alert
          message="检测到格式问题"
          description={
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              {issues.map((issue, index) => (
                <li key={index}>{issue}</li>
              ))}
            </ul>
          }
          type="warning"
          icon={<WarningOutlined />}
          style={{ marginBottom: 12 }}
          action={
            <Button size="small" type="primary" onClick={handleFormat}>
              立即修复
            </Button>
          }
        />
      ) : (
        <Alert
          message="格式检查通过"
          description="文本格式符合Word文档要求"
          type="success"
          style={{ marginBottom: 12 }}
          showIcon
        />
      )}

      {/* 文本输入区域 - 可收起 */}
      {!isCollapsed && (
        <div style={{ marginBottom: 12 }}>
          <Text strong>原始文本：</Text>
          <TextArea
            value={formattedText}
            onChange={(e) => setFormattedText(e.target.value)}
            placeholder={placeholder || "请输入需要格式化的文本..."}
            rows={4}
            style={{ marginTop: 8 }}
          />
        </div>
      )}

      {/* 操作按钮 */}
      <Space>
        <Button
          type="primary"
          icon={<FormatPainterOutlined />}
          onClick={handleFormat}
          loading={isFormatting}
          size="small"
        >
          格式化文本
        </Button>

        {!isCollapsed && (
          <>
            <Button
              icon={<CheckOutlined />}
              onClick={handleApply}
              disabled={!formattedText}
              size="small"
            >
              应用结果
            </Button>

            <Button
              icon={<ClearOutlined />}
              onClick={handleClear}
              size="small"
            >
              清空
            </Button>
          </>
        )}
      </Space>

      <Divider />

      {/* 格式化说明 */}
      <div>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          <FileTextOutlined /> Word文档格式化规则：
          清理HTML标签 → 保留基本格式（**粗体** *斜体* _下划线_） → 规范空白字符 → 统一换行符 → 移除多余空行
        </Text>
        <br />
        <Text type="secondary" style={{ fontSize: '11px' }}>
          💡 提示：格式化后的内容将在Word文档中显示为黑体字，并包含在框框中
        </Text>
      </div>

      {/* 预览区域 - 可收起 */}
      {!isCollapsed && formattedText && (
        <div style={{ marginTop: 12 }}>
          <Text strong>格式化预览：</Text>
          <div
            style={{
              background: '#f5f5f5',
              padding: 12,
              borderRadius: 4,
              marginTop: 8,
              maxHeight: 150,
              overflow: 'auto',
              whiteSpace: 'pre-wrap',
              fontFamily: 'monospace',
              fontSize: '12px'
            }}
          >
            {formattedText}
          </div>
        </div>
      )}
    </Card>
  );
}
